{
    "name" : "CIMOM-UNI-APP",
    "appid" : "__UNI__BA0A158",
    "description" : "CIMOM-UNI-APP",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "app-plus" : {
        "safearea" : {
            //安全区域配置，仅iOS平台生效  
            "bottom" : {
                // 底部安全区域配置  
                "offset" : "none" // 底部安全区域偏移，"none"表示不空出安全区域，"auto"自动计算空出安全区域，默认值为"none"  
            }
        },
        /* 5+App特有相关 */
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "nvueStyleCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "Geolocation" : {},
            "Barcode" : {},
            "Bluetooth" : {},
            "Camera" : {},
            "Maps" : {},
            "Push" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {
                "dSYMs" : false
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "geolocation" : {
                    "amap" : {
                        "name" : "amapxmCLRwSf",
                        "__platform__" : [ "android" ],
                        "appkey_ios" : "",
                        "appkey_android" : "f92598b286d91fc2ba1913cfab7d2fe1"
                    }
                },
                "maps" : {
                    "amap" : {
                        "name" : "amapxmCLRwSf",
                        "appkey_ios" : "f92598b286d91fc2ba1913cfab7d2fe1",
                        "appkey_android" : "f92598b286d91fc2ba1913cfab7d2fe1"
                    }
                },
                "push" : {
                    "unipush" : {
                        "appid" : "vMyQRs61ii8djre1VbC2x5", // 你的 DCloud 应用 ID
                        "appkey" : "g8hQpj1tnZ7CPjk4vel8h3", // 你的 DCloud 应用密钥
                        "secret" : "bWwuYC8kp29g0dhzJb1Yj", // 你的 DCloud 应用密钥             
                        "offline" : true,
                        "hms" : {},
                        "mi" : {},
                        "vivo" : {},
                        "oppo" : {},
                        "meizu" : {},
                        "honor" : {},
                        "fcm" : {}
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "static/images/first1.png",
                    "xhdpi" : "static/images/first1.png",
                    "xxhdpi" : "static/images/first1.png"
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "appid" : "wxa51f53b70fc94dbf",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "vueVersion" : "3",
    "app-harmony" : {
        "projectPath" : "D:\\hongmengOs\\CIMOM-UNI-APP",
        "distribute" : {
            "bundleName" : "uni.app.UNIBA0A158",
            "signingConfigs" : {
                "default" : {
                    "certpath" : "c:\\Users\\<USER>\\AppData\\Roaming\\HBuilder X\\extensions\\launcher\\agc-certs\\1747899573341.cer",
                    "keyAlias" : "debugKey",
                    "keyPassword" : "0000001B82E4D7F4062821B39DBB717E05B74575604554CA6962E03511B0205047707CD31F25EAD239DF4C",
                    "profile" : "c:\\Users\\<USER>\\AppData\\Roaming\\HBuilder X\\extensions\\launcher\\agc-certs\\1747899573341.p7b",
                    "signAlg" : "SHA256withECDSA",
                    "storeFile" : "c:\\Users\\<USER>\\AppData\\Roaming\\HBuilder X\\extensions\\launcher\\agc-certs\\1745811186665.p12",
                    "storePassword" : "0000001B82E4D7F4062821B39DBB717E05B74575604554CA6962E03511B0205047707CD31F25EAD239DF4C"
                }
            },
            "icons" : {
                "foreground" : "unpackage/res/icons/1024x1024.png",
                "background" : "static/images/bg/bg.png"
            },
            "splashScreens" : {
                "startWindowIcon" : "unpackage/res/icons/1024x1024.png"
            },
            // ---------------------- 新增/修改 HarmonyOS SDK 版本配置 ----------------------
            "compatibleVersion" : "3", // 指定兼容 HarmonyOS 4.0（必填）
            "targetSdkVersion" : "4" // 目标 SDK 版本（可选，建议与系统版本一致）
        }
    },
    "locale" : "zh-Hans",
    "fallbackLocale" : "zh-Hans"
}
