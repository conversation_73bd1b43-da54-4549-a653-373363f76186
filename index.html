<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
	<!-- <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=kz26jZQiN4x1rkRAQhW0vTcnUet34OsD"></script> -->
    <title></title>
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
	 
    <div id="app"><!--app-html--></div>
    <script type="module" src="/main.js"></script>
	 <script  src="./static/iconfont.js"></script>
	  <link href="./static/fontawesome/css/fontawesome.css" rel="stylesheet" />
	  <link href="./static/fontawesome/css/brands.css" rel="stylesheet" />
	  <link href="./static/fontawesome/css/solid.css" rel="stylesheet" />
	 <!-- <link href="./static/fontawesome/css/sharp-thin.css" rel="stylesheet" />
	  <link href="./static/fontawesome/css/duotone-thin.css" rel="stylesheet" />
	  <link href="./static/fontawesome/css/sharp-duotone-thin.css" rel="stylesheet" /> -->
	<!-- fontawesome图标使用方式 <i class="fa-solid fa-user"></i> -->
  </body>
</html>
