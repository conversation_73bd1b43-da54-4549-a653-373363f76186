
// import { getStorageSync } from '@/utils/Storage.js';
import {
	setStorageSync,
	getStorageSync,
} from '@/utils/Storage.js'
/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息对象，若不存在则返回null
 */
/**
 * 获取当前用户指定字段值
 * @param {string} [field] 可选，指定字段名，返回该字段值
 * @param {any} [defalutValue] 若不存在，返回defalutValue
 * @returns {any} 用户信息指定字段值，若不存在返回null
 */
export function getCurrentUserInfoByField(field,defalutValue=null) {
    try {
         const userInfo = getStorageSync('USER_INFO') || null;
        if (!userInfo) return null;
        if (field) {
            return userInfo[field] !== undefined ? userInfo[field] : null;
        }
    } catch (error) {
         return defalutValue;
    }
   
    return defalutValue;
}

//请求日志处理，只存储前20条，大于20条后要删除 存储到RequestLog
export function handleRequestLog(response) {
    response.time = new Date().toLocaleString();
    let requestLog = getStorageSync('RequestLog') || [];
    requestLog.unshift(response);
    if (requestLog.length > 20) {
        requestLog.pop();
    }
    setStorageSync('RequestLog', requestLog);
    return requestLog;
}

//返回数据日志处理，只存储前20条，大于20条后要删除 存储到 ResponseLog
export function handleResponseLog(response) {
   // debugger
    response.time = new Date().toLocaleString();
    let responseLog = getStorageSync('ResponseLog') || [];
    responseLog.unshift(response);
    if (responseLog.length > 20) {
        responseLog.pop();
    }
    setStorageSync('ResponseLog', responseLog);
    return responseLog;
}

// 清空需要清空的登录缓存数据，退出时使用
export function cleanCache() {
    setStorageSync('ResponseLog',null)
    setStorageSync('RequestLog',null);
    setStorageSync('MESSAGE_LIST',null)
    setStorageSync('token', '')
    setStorageSync('ModuleData', null)
    setStorageSync('userInfo', null)
    setStorageSync('USER_INFO', null)
    setStorageSync('login_params', null)
    setStorageSync('serverip', '')
    setStorageSync('PLUGIN_LIST', null)
    setStorageSync('Favourite', null)
}

export function getValueType(value) {
  // 处理 null
  if (value === null) return 'null';
  
  // 处理基本类型
  const basicType = typeof value;
  if (!['object', 'function'].includes(basicType)) {
    return basicType;
  }
  
  // 处理引用类型（通过 toString 提取类型名）
  const typeStr = Object.prototype.toString.call(value);
  return typeStr.slice(8, -1).toLowerCase();
}

// 测试
// getValueType('hello'); // 'string'
// getValueType(123); // 'number'
// getValueType(true); // 'boolean'
// getValueType(undefined); // 'undefined'
// getValueType(null); // 'null'
// getValueType([]); // 'array'
// getValueType({}); // 'object'
// getValueType(new Date()); // 'date'
// getValueType(function(){}); // 'function'
// getValueType(Symbol('id')); // 'symbol'
// getValueType(123n); // 'bigint'
