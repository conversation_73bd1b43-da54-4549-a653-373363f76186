<template>
	<view class="flex h-screen bg-color-main flex-col justify-center items-center">
		<!-- <view v-html="messageDetailHtml"></view> -->
		<!-- <uni-tag size="normal" :text="messageDetailHtml" type="primary" /> -->
		<uni-notice-bar :text="messageDetailHtml" ></uni-notice-bar>
	</view>
</template>

<script setup>
	import { getValueType } from '@/utils/tools.js'
	import * as serviceApi from '@/api/index.js'
	import {
		ref,
		computed,
		onMounted,
		getCurrentInstance
	} from 'vue';
	import { onLoad } from '@dcloudio/uni-app';
	
	const messageDetailHtml =ref('')
	
	onLoad((option)=>{
		console.log('============onLoad==========option===', JSON.stringify(option))
		handleOnLoadData(option)
	})
	//////////////////////methods//////////////////////////
	// 处理跳转过来的页面路径参数
	// {title: '通知消息', CID: '1111111'}
	function handleOnLoadData(option){
		let _title = decodeURIComponent(option.title)
		if(option.title && option.CID){
			uni.setNavigationBarTitle({
					title: _title
			});
			loadMessageDetail(option.CID)
		}
		
	}
	// 加载小心详情
	function loadMessageDetail(_id){
		let params ={
			id:_id
		}
		serviceApi.getNoticeDetail(null,params).then(res => {
			if (res && res.data.code === 200 && res.data.data.Success) {
				//messageDetailHtml.value =  res.data.data.Datas
				handleMessageResData(res.data.data.Datas)
			} 
		}).catch(err => {
			uni.showToast({
				title: '消息详情服务异常，请重试',
				icon: 'none'
			})
		}).finally(() => {
			
		})
	}
	
	// 处理消息返回的数据，根据返回值-类型处理
	function handleMessageResData(resData){
		if(resData){
			//messageDetailHtml.value =  res.data.data.Datas
			if(getValueType(resData)=='object'){
				messageDetailHtml.value =  resData.CCONTENT//JSON.stringify(resData)
			}
		}
	}
	
</script>

<style>
	       
</style>