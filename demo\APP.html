<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>首页</title>
    <!-- Tailwind CSS（快速布局） -->
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
       <script src="./tailwindcss.3.4.17.js"></script>
    <!-- ECharts（数据可视化） -->
    <!-- <script src="https://cdn.staticfile.net/echarts/4.7.0/echarts.min.js"></script> -->
     <script src="./echarts.min.js"></script>
     <script src="./uni.webview.1.5.2.js"></script>
    <style>
        .chart-box { height: 240px; } /* 图表容器高度 */
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- 顶部导航栏（固定） -->
   

    <main class="pt-4 pb-10 px-4">
        <!-- 快捷功能区 -->
        <section class="mb-6">
            <!-- 功能区标题和折叠按钮 -->
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-blue-800">快捷功能</h2>
                <button id="toggleMainFunctions" class="text-blue-600 hover:text-blue-800 transition-colors">
                    <svg id="toggleIcon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
                    </svg>
                </button>
            </div>

            <!-- 主功能区（可折叠） -->
            <div id="mainFunctionsContainer" class="transition-all duration-300 ease-in-out">
                <!-- 大卡片：核心操作 2x2布局 -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <!-- 过站扫描 -->
                    <div class="bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl p-6 flex items-center justify-between shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                        <div id="transitScanFn">
                            <h3 class="text-lg font-semibold text-blue-800 mb-1">过站扫描</h3>
                            <p class="text-sm text-blue-600">按产品工艺流程过站</p>
                        </div>
                        <div class="bg-blue-500 rounded-full p-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="white" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 013.75 9.375v-4.5zM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 01-1.125-1.125v-4.5zM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0113.5 9.375v-4.5z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 6.75h.75v.75h-.75v-.75zM6.75 16.5h.75v.75h-.75v-.75zM16.5 6.75h.75v.75h-.75v-.75zM13.5 13.5h4.5v4.5h-4.5v-4.5z" />
                            </svg>
                        </div>
                    </div>

                    <!-- 信息中心 -->
                    <div class="bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl p-6 flex items-center justify-between shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                        <div>
                            <h3 class="text-lg font-semibold text-purple-800 mb-1">信息中心</h3>
                            <p class="text-sm text-purple-600">设备与生产信息</p>
                        </div>
                        <div class="bg-purple-500 rounded-full p-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="white" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.651a3.75 3.75 0 010-5.303m5.304 0a3.75 3.75 0 010 5.303m-7.425 2.122a6.75 6.75 0 010-9.546m9.546 0a6.75 6.75 0 010 9.546M5.106 18.894c3.808-3.808 9.98-3.808 13.788 0M12 12h.008v.008H12V12z" />
                            </svg>
                        </div>
                    </div>

                    <!-- 库存查询 -->
                    <div class="bg-gradient-to-br from-green-100 to-green-200 rounded-xl p-6 flex items-center justify-between shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                        <div>
                            <h3 class="text-lg font-semibold text-green-800 mb-1">库存查询</h3>
                            <p class="text-sm text-green-600">实时库存状态</p>
                        </div>
                        <div class="bg-green-500 rounded-full p-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="white" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
                            </svg>
                        </div>
                    </div>

                    <!-- 第四个功能区域 -->
                    <div class="bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl p-6 flex items-center justify-between shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                        <div>
                            <h3 class="text-lg font-semibold text-orange-800 mb-1">生产管理</h3>
                            <p class="text-sm text-orange-600">生产计划与调度</p>
                        </div>
                        <div class="bg-orange-500 rounded-full p-3">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="white" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75" />
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 小按钮：辅助操作 -->
                <div class="grid grid-cols-4 gap-4">
                    <!-- 产品档案 -->
                    <button class="bg-white rounded-lg p-4 flex flex-col items-center justify-center hover:bg-gray-50 transition-colors shadow-sm border border-gray-100">
                        <div class="bg-gray-100 rounded-full p-2 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gray-600">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h6.75" />
                            </svg>
                        </div>
                        <span class="text-xs text-gray-700 font-medium">产品档案</span>
                    </button>
                    <!-- 点检记录 -->
                    <button class="bg-white rounded-lg p-4 flex flex-col items-center justify-center hover:bg-gray-50 transition-colors shadow-sm border border-gray-100">
                        <div class="bg-green-100 rounded-full p-2 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-green-600">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <span class="text-xs text-gray-700 font-medium">点检记录</span>
                    </button>
                    <!-- 保养记录 -->
                    <button class="bg-white rounded-lg p-4 flex flex-col items-center justify-center hover:bg-gray-50 transition-colors shadow-sm border border-gray-100">
                        <div class="bg-blue-100 rounded-full p-2 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-blue-600">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z" />
                            </svg>
                        </div>
                        <span class="text-xs text-gray-700 font-medium">保养记录</span>
                    </button>
                    <!-- 品质记录 -->
                    <button class="bg-white rounded-lg p-4 flex flex-col items-center justify-center hover:bg-gray-50 transition-colors shadow-sm border border-gray-100">
                        <div class="bg-pink-100 rounded-full p-2 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-pink-600">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
                            </svg>
                        </div>
                        <span class="text-xs text-gray-700 font-medium">品质记录</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- 数据统计区 -->
        <section class="space-y-6">
            <!-- 设备状态（饼图） -->
            <div class="bg-white rounded-lg shadow">
                <div class="flex justify-between items-center p-4 border-b border-gray-100">
                    <h2 class="text-lg font-semibold text-blue-800">设备状态统计</h2>
                    <button class="chart-toggle text-blue-600 hover:text-blue-800 transition-colors" data-target="deviceStatusChart">
                        <svg class="w-5 h-5 transform transition-transform" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
                        </svg>
                    </button>
                </div>
                <div id="deviceStatusChart" class="chart-container transition-all duration-300 ease-in-out">
                    <div class="chart-box p-4" id="deviceStatus"></div>
                </div>
            </div>

            <!-- 稼动率（折线图） -->
            <div class="bg-white rounded-lg shadow">
                <div class="flex justify-between items-center p-4 border-b border-gray-100">
                    <h2 class="text-lg font-semibold text-blue-800">设备稼动率统计</h2>
                    <button class="chart-toggle text-blue-600 hover:text-blue-800 transition-colors" data-target="utilizationChart">
                        <svg class="w-5 h-5 transform transition-transform" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
                        </svg>
                    </button>
                </div>
                <div id="utilizationChart" class="chart-container transition-all duration-300 ease-in-out">
                    <div class="chart-box p-4" id="稼动率"></div>
                </div>
            </div>

            <!-- 产量（柱状图） -->
            <div class="bg-white rounded-lg shadow">
                <div class="flex justify-between items-center p-4 border-b border-gray-100">
                    <h2 class="text-lg font-semibold text-blue-800">生产产量统计</h2>
                    <button class="chart-toggle text-blue-600 hover:text-blue-800 transition-colors" data-target="productionChart">
                        <svg class="w-5 h-5 transform transition-transform" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
                        </svg>
                    </button>
                </div>
                <div id="productionChart" class="chart-container transition-all duration-300 ease-in-out">
                    <div class="chart-box p-4" id="产量"></div>
                </div>
            </div>
        </section>
    </main>

    <!-- ECharts 初始化和折叠功能 -->
    <script>
        // 折叠/展开功能
        document.addEventListener('DOMContentLoaded', function() {
            // 主功能区折叠
            
            const transitScanFn = document.getElementById('transitScanFn');
            // 过站扫描 点击 跳转
             transitScanFn.addEventListener('click', function() {
				let _title=encodeURIComponent('过站扫描')
                uni.navigateTo({
                    url: `/pages/common/common?title=过站扫描&url=WMS@http:/App/WMS/UIDReceiving`,
                   
                });
             });
            const toggleMainBtn = document.getElementById('toggleMainFunctions');
            const mainContainer = document.getElementById('mainFunctionsContainer');
            const toggleIcon = document.getElementById('toggleIcon');
            let isMainCollapsed = false;
          
            toggleMainBtn.addEventListener('click', function() {
                isMainCollapsed = !isMainCollapsed;

                if (isMainCollapsed) {
                    mainContainer.style.maxHeight = '0';
                    mainContainer.style.opacity = '0';
                    mainContainer.style.overflow = 'hidden';
                    toggleIcon.style.transform = 'rotate(180deg)';
                } else {
                    mainContainer.style.maxHeight = 'none';
                    mainContainer.style.opacity = '1';
                    mainContainer.style.overflow = 'visible';
                    toggleIcon.style.transform = 'rotate(0deg)';
                }
            });

            // 图表区域折叠
            const chartToggles = document.querySelectorAll('.chart-toggle');
            chartToggles.forEach(toggle => {
                let isCollapsed = false;

                toggle.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const targetContainer = document.getElementById(targetId);
                    const icon = this.querySelector('svg');

                    isCollapsed = !isCollapsed;

                    if (isCollapsed) {
                        targetContainer.style.maxHeight = '0';
                        targetContainer.style.opacity = '0';
                        targetContainer.style.overflow = 'hidden';
                        icon.style.transform = 'rotate(180deg)';
                    } else {
                        targetContainer.style.maxHeight = 'none';
                        targetContainer.style.opacity = '1';
                        targetContainer.style.overflow = 'visible';
                        icon.style.transform = 'rotate(0deg)';
                    }
                });
            });
        });

        // 1. 设备状态饼图（数据：工作17、停机2、离线0、报警3）
        var deviceChart = echarts.init(document.getElementById('deviceStatus'));
        deviceChart.setOption({
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                top: '10%',
                left: 'center',
                data: ['工作', '停机', '离线', '报警'],
                textStyle: {
                    fontSize: 12
                }
            },
            series: [{
                name: '设备状态',
                type: 'pie',
                radius: ['0%', '65%'], // 实心饼图，尽可能大
                center: ['50%', '60%'], // 向下移动给图例留空间
                data: [
                    { value: 17, name: '工作', itemStyle: { color: '#1677ff' } },
                    { value: 2, name: '停机', itemStyle: { color: '#87d068' } },
                    { value: 0, name: '离线', itemStyle: { color: '#ffc800' } },
                    { value: 3, name: '报警', itemStyle: { color: '#ff4d4f' } }
                ],
                label: {
                    show: true,
                    position: 'outside',
                    formatter: '{b}:{c}',
                    fontSize: 12
                },
                labelLine: {
                    show: true
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        });

        // 2. 稼动率折线图（扩展数据，支持拖拽）
        var yidongChart = echarts.init(document.getElementById('稼动率'));

        // 生成更多日期数据
        var dates = [];
        var whiteShiftData = [];
        var nightShiftData = [];
        var allDayData = [];

        for (let i = 0; i < 30; i++) {
            var date = new Date();
            date.setDate(date.getDate() - 29 + i);
            dates.push(date.toISOString().split('T')[0]);

            // 生成模拟数据
            whiteShiftData.push(Math.floor(Math.random() * 30) + 10);
            nightShiftData.push(Math.floor(Math.random() * 35) + 15);
            allDayData.push(Math.floor(Math.random() * 40) + 20);
        }

        yidongChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                },
                formatter: function(params) {
                    var result = params[0].axisValue + '<br/>';
                    params.forEach(function(item) {
                        result += item.marker + item.seriesName + ': ' + item.value + '%<br/>';
                    });
                    return result;
                }
            },
            legend: {
                data: ['白班', '晚班', '全天'],
                top: '5%',
                textStyle: {
                    fontSize: 12
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '20%',
                containLabel: true
            },
            // toolbox: {
            //     feature: {
            //         dataZoom: {
            //             yAxisIndex: 'none'
            //         },
            //         restore: {},
            //         saveAsImage: {}
            //     }
            // },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: dates,
                axisLabel: {
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            dataZoom: [{
                type: 'inside',
                start: 70,
                end: 100
            }, {
                start: 70,
                end: 100,
                handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                handleSize: '80%',
                handleStyle: {
                    color: '#fff',
                    shadowBlur: 3,
                    shadowColor: 'rgba(0, 0, 0, 0.6)',
                    shadowOffsetX: 2,
                    shadowOffsetY: 2
                }
            }],
            series: [
                {
                    name: '白班',
                    type: 'line',
                    stack: 'Total',
                    data: whiteShiftData,
                    lineStyle: { color: '#1677ff', width: 2 },
                    itemStyle: { color: '#1677ff' },
                    areaStyle: { opacity: 0.1, color: '#1677ff' }
                },
                {
                    name: '晚班',
                    type: 'line',
                    stack: 'Total',
                    data: nightShiftData,
                    lineStyle: { color: '#87d068', width: 2 },
                    itemStyle: { color: '#87d068' },
                    areaStyle: { opacity: 0.1, color: '#87d068' }
                },
                {
                    name: '全天',
                    type: 'line',
                    stack: 'Total',
                    data: allDayData,
                    lineStyle: { color: '#ffc800', width: 2 },
                    itemStyle: { color: '#ffc800' },
                    areaStyle: { opacity: 0.1, color: '#ffc800' }
                }
            ]
        });

        // 3. 产量柱状图（扩展数据，支持拖拽）
        var chanliangChart = echarts.init(document.getElementById('产量'));

        // 生成更多产量数据
        var productionDates = [];
        var productionData = [];

        for (let i = 0; i < 30; i++) {
            var date = new Date();
            date.setDate(date.getDate() - 29 + i);
            productionDates.push(date.toISOString().split('T')[0]);

            // 生成模拟产量数据 (100-500之间)
            productionData.push(Math.floor(Math.random() * 400) + 100);
        }

        chanliangChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function(params) {
                    return params[0].axisValue + '<br/>' +
                           params[0].marker + params[0].seriesName + ': ' + params[0].value + '件';
                }
            },
            legend: {
                data: ['总产量'],
                top: '5%',
                textStyle: {
                    fontSize: 12
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '20%',
                containLabel: true
            },
            // toolbox: {
            //     feature: {
            //         dataZoom: {
            //             yAxisIndex: 'none'
            //         },
            //         restore: {},
            //         saveAsImage: {}
            //     }
            // },
            xAxis: {
                type: 'category',
                data: productionDates,
                axisLabel: {
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value}件'
                }
            },
            dataZoom: [{
                type: 'inside',
                start: 70,
                end: 100
            }, {
                start: 70,
                end: 100,
                handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                handleSize: '80%',
                handleStyle: {
                    color: '#fff',
                    shadowBlur: 3,
                    shadowColor: 'rgba(0, 0, 0, 0.6)',
                    shadowOffsetX: 2,
                    shadowOffsetY: 2
                }
            }],
            series: [{
                name: '总产量',
                type: 'bar',
                data: productionData,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#83bff6' },
                        { offset: 0.5, color: '#188df0' },
                        { offset: 1, color: '#188df0' }
                    ])
                },
                emphasis: {
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#2378f7' },
                            { offset: 0.7, color: '#2378f7' },
                            { offset: 1, color: '#83bff6' }
                        ])
                    }
                },
                markPoint: {
                    data: [
                        { type: 'max', name: '最大值' },
                        { type: 'min', name: '最小值' }
                    ]
                },
                markLine: {
                    data: [
                        { type: 'average', name: '平均值' }
                    ]
                }
            }]
        });

        // 窗口大小改变时重新调整图表
        window.addEventListener('resize', function() {
            deviceChart.resize();
            yidongChart.resize();
            chanliangChart.resize();
        });
    </script>
</body>
</html>